"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { UserPlus, Building, Users } from "lucide-react"
import { EmployeeFormDialog } from "@/components/employee-form-dialog"
import { ManagerFormDialog } from "@/components/manager-form-dialog"
import { DepartmentFormDialog } from "@/components/department-form-dialog"
import type { Department, Manager } from "@/lib/types"

export default function AddPeoplePage() {
  const [activeTab, setActiveTab] = useState<"employee" | "manager" | "department">("employee")
  const [isEmployeeDialogOpen, setIsEmployeeDialogOpen] = useState(false)
  const [isManagerDialogOpen, setIsManagerDialogOpen] = useState(false)
  const [isDepartmentDialogOpen, setIsDepartmentDialogOpen] = useState(false)

  const handleDialogClose = () => {
    setIsEmployeeDialogOpen(false)
    setIsManagerDialogOpen(false)
    setIsDepartmentDialogOpen(false)
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold mb-2">Add People & Departments</h1>
        <p className="text-muted-foreground">
          Add new employees, managers, or departments to the system
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex mb-6 border-b">
        <button
          onClick={() => setActiveTab("employee")}
          className={`px-4 py-2 font-medium border-b-2 transition-colors ${
            activeTab === "employee"
              ? "border-primary text-primary"
              : "border-transparent text-muted-foreground hover:text-foreground"
          }`}
        >
          <Users className="w-4 h-4 inline mr-2" />
          Employee
        </button>
        <button
          onClick={() => setActiveTab("manager")}
          className={`px-4 py-2 font-medium border-b-2 transition-colors ${
            activeTab === "manager"
              ? "border-primary text-primary"
              : "border-transparent text-muted-foreground hover:text-foreground"
          }`}
        >
          <UserPlus className="w-4 h-4 inline mr-2" />
          Manager
        </button>
        <button
          onClick={() => setActiveTab("department")}
          className={`px-4 py-2 font-medium border-b-2 transition-colors ${
            activeTab === "department"
              ? "border-primary text-primary"
              : "border-transparent text-muted-foreground hover:text-foreground"
          }`}
        >
          <Building className="w-4 h-4 inline mr-2" />
          Department
        </button>
      </div>

      {/* Employee Section */}
      {activeTab === "employee" && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Employee</CardTitle>
            <CardDescription>
              Click the button below to open the employee creation form
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => setIsEmployeeDialogOpen(true)}
              className="w-full"
              size="lg"
            >
              <Users className="w-4 h-4 mr-2" />
              Add Employee
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Manager Section */}
      {activeTab === "manager" && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Manager</CardTitle>
            <CardDescription>
              Click the button below to open the manager creation form
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => setIsManagerDialogOpen(true)}
              className="w-full"
              size="lg"
            >
              <UserPlus className="w-4 h-4 mr-2" />
              Add Manager
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Department Section */}
      {activeTab === "department" && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Department</CardTitle>
            <CardDescription>
              Click the button below to open the department creation form
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => setIsDepartmentDialogOpen(true)}
              className="w-full"
              size="lg"
            >
              <Building className="w-4 h-4 mr-2" />
              Add Department
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Dialog Components */}
      <EmployeeFormDialog
        isOpen={isEmployeeDialogOpen}
        onClose={handleDialogClose}
        employee={null}
        departments={[]}
        managers={[]}
      />

      <ManagerFormDialog
        isOpen={isManagerDialogOpen}
        onClose={handleDialogClose}
        manager={null}
        departments={[]}
      />

      <DepartmentFormDialog
        isOpen={isDepartmentDialogOpen}
        onClose={handleDialogClose}
        department={null}
      />
    </div>
  )
}