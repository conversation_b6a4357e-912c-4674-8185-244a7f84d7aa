"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { AlertTriangle, Loader } from "lucide-react"
import type { Department, Manager } from "@/lib/types"
import { managerFormSchema } from "@/lib/schemas"
import { saveManagerAction } from "@/lib/actions"
import { useTransition } from "react"
import { z } from "zod"

interface ManagerFormDialogProps {
  isOpen: boolean
  onClose: () => void
  manager: Manager | null
  departments?: Department[]
}

type ManagerFormData = z.infer<typeof managerFormSchema>

export function ManagerFormDialog({ isOpen, onClose, manager, departments = [] }: ManagerFormDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = React.useState<string | null>(null)

  const form = useForm<ManagerFormData>({
    resolver: zodResolver(managerFormSchema),
    defaultValues: {
      fullName: "",
      email: "",
      role: "manager",
      departmentId: "",
    },
  })

  React.useEffect(() => {
    if (manager) {
      form.reset({
        fullName: manager.fullName,
        email: manager.email || "",
        role: manager.role || "manager",
        departmentId: manager.departmentId || "",
      })
    } else {
      form.reset({
        fullName: "",
        email: "",
        role: "manager",
        departmentId: "",
      })
    }
  }, [manager, form])

  const onSubmit = (data: ManagerFormData) => {
    setSubmitError(null)
    
    startTransition(async () => {
      try {
        const formData = new FormData()
        formData.append('fullName', data.fullName)
        formData.append('email', data.email)
        formData.append('role', data.role)
        if (data.departmentId) {
          formData.append('departmentId', data.departmentId)
        }

        const result = await saveManagerAction(formData)
        
        if (result.success) {
          form.reset()
          onClose()
        } else {
          setSubmitError(result.error || 'Failed to save manager')
        }
      } catch (error) {
        setSubmitError('An unexpected error occurred')
        console.error('Error saving manager:', error)
      }
    })
  }

  const handleClose = () => {
    if (!isPending) {
      form.reset()
      setSubmitError(null)
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {manager ? 'Edit Manager' : 'Add New Manager'}
          </DialogTitle>
          <DialogDescription>
            {manager ? 'Update manager information.' : 'Create a new manager account.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter full name" 
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input 
                      type="email"
                      placeholder="Enter email address" 
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isPending}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="manager">Manager</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="hr-admin">HR Admin</SelectItem>
                      <SelectItem value="accountant">Accountant</SelectItem>
                      <SelectItem value="super-admin">Super Admin</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="departmentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isPending}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="">No Department</SelectItem>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id}>
                          {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {submitError && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{submitError}</AlertDescription>
              </Alert>
            )}

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleClose}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isPending}
              >
                {isPending && <Loader className="mr-2 h-4 w-4 animate-spin" />}
                {manager ? 'Update Manager' : 'Create Manager'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}